<?php

namespace LBCDev\OAuthManager\Tests\Unit;

use Tests\TestCase;
use Illuminate\Support\Facades\Config;
use LBCDev\OAuthManager\Models\OAuthService;
use Illuminate\Foundation\Testing\RefreshDatabase;

class OAuthServiceTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->app['config']->set('oauth-manager.services.google_drive', [
            'name' => 'Google Drive',
            'icon' => 'heroicon-o-folder',
            'provider' => \LBCDev\OAuthManager\Providers\GoogleDriveProvider::class,
            'scopes' => ['https://www.googleapis.com/auth/drive'],
            'fields' => [
                'client_id' => 'Client ID',
                'client_secret' => 'Client Secret',
            ]
        ]);
    }

    public function test_can_instantiate_provider()
    {
        $service = new OAuthService([
            'name' => 'Test Google Drive',
            'service_type' => 'google_drive',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
        ]);

        $provider = $service->getProviderInstance();

        $this->assertInstanceOf(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class, $provider);
    }

    public function test_throws_exception_for_unknown_service_type()
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Service type unknown_service not configured');

        $service = new OAuthService([
            'name' => 'Test Unknown Service',
            'service_type' => 'unknown_service',
            'credentials' => [],
        ]);

        $service->getProviderInstance();
    }

    public function test_needs_refresh_returns_true_for_expired_token_with_refresh_token()
    {
        $service = new OAuthService([
            'access_token' => 'expired_token',
            'refresh_token' => 'valid_refresh_token',
            'expires_at' => now()->subHour(),
        ]);

        $this->assertTrue($service->needsRefresh());
    }

    public function test_needs_refresh_returns_false_for_expired_token_without_refresh_token()
    {
        $service = new OAuthService([
            'access_token' => 'expired_token',
            'refresh_token' => null,
            'expires_at' => now()->subHour(),
        ]);

        $this->assertFalse($service->needsRefresh());
    }

    public function test_needs_refresh_returns_false_for_valid_token()
    {
        $service = new OAuthService([
            'access_token' => 'valid_token',
            'refresh_token' => 'valid_refresh_token',
            'expires_at' => now()->addHour(),
        ]);

        $this->assertFalse($service->needsRefresh());
    }

    public function test_is_token_expired_returns_true_for_expired_token()
    {
        $service = new OAuthService([
            'access_token' => 'expired_token',
            'expires_at' => now()->subHour(),
        ]);

        $this->assertTrue($service->isTokenExpired());
    }

    public function test_is_token_expired_returns_false_for_valid_token()
    {
        $service = new OAuthService([
            'access_token' => 'valid_token',
            'expires_at' => now()->addHour(),
        ]);

        $this->assertFalse($service->isTokenExpired());
    }

    public function test_is_token_expired_returns_false_for_null_expires_at()
    {
        $service = new OAuthService([
            'access_token' => 'token_without_expiry',
            'expires_at' => null,
        ]);

        $this->assertFalse($service->isTokenExpired());
    }
}
