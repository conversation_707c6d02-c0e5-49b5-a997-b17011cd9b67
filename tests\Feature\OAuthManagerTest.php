<?php

// tests/Feature/OAuthManagerTest.php
namespace LBCDev\OAuthManager\Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use LBCDev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Services\OAuthManager;
use Illuminate\Support\Facades\Schema;

class OAuthManagerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create oauth_services table manually for testing
        if (!Schema::hasTable('oauth_services')) {
            Schema::create('oauth_services', function ($table) {
                $table->id();
                $table->string('name');
                $table->string('service_type');
                $table->json('credentials');
                $table->text('access_token')->nullable();
                $table->text('refresh_token')->nullable();
                $table->timestamp('expires_at')->nullable();
                $table->boolean('is_active')->default(true);
                $table->timestamp('last_used_at')->nullable();
                $table->timestamps();

                $table->unique(['service_type', 'name']);
            });
        }
    }

    public function test_can_create_oauth_service()
    {
        $service = OAuthService::create([
            'name' => 'Test Google Drive',
            'service_type' => 'google_drive',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
            'is_active' => true,
        ]);

        $this->assertInstanceOf(OAuthService::class, $service);
        $this->assertEquals('Test Google Drive', $service->name);
        $this->assertEquals('google_drive', $service->service_type);
        $this->assertTrue($service->is_active);
    }

    public function test_can_get_service_by_type()
    {
        $service = OAuthService::create([
            'name' => 'Test Google Drive',
            'service_type' => 'google_drive',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
            'is_active' => true,
        ]);

        $oauthManager = new OAuthManager();
        $foundService = $oauthManager->getService('google_drive');

        $this->assertInstanceOf(OAuthService::class, $foundService);
        $this->assertEquals($service->id, $foundService->id);
    }

    public function test_can_get_service_by_type_and_name()
    {
        OAuthService::create([
            'name' => 'Primary Google Drive',
            'service_type' => 'google_drive',
            'credentials' => ['client_id' => 'test1', 'client_secret' => 'secret1'],
            'is_active' => true,
        ]);

        $service2 = OAuthService::create([
            'name' => 'Secondary Google Drive',
            'service_type' => 'google_drive',
            'credentials' => ['client_id' => 'test2', 'client_secret' => 'secret2'],
            'is_active' => true,
        ]);

        $oauthManager = new OAuthManager();
        $foundService = $oauthManager->getService('google_drive', 'Secondary Google Drive');

        $this->assertEquals($service2->id, $foundService->id);
    }

    public function test_returns_null_for_inactive_service()
    {
        OAuthService::create([
            'name' => 'Inactive Google Drive',
            'service_type' => 'google_drive',
            'credentials' => ['client_id' => 'test', 'client_secret' => 'secret'],
            'is_active' => false,
        ]);

        $oauthManager = new OAuthManager();
        $foundService = $oauthManager->getService('google_drive');

        $this->assertNull($foundService);
    }

    public function test_can_detect_expired_token()
    {
        $service = OAuthService::create([
            'name' => 'Test Google Drive',
            'service_type' => 'google_drive',
            'credentials' => ['client_id' => 'test', 'client_secret' => 'secret'],
            'access_token' => 'expired_token',
            'expires_at' => now()->subHour(),
            'is_active' => true,
        ]);

        $this->assertTrue($service->isTokenExpired());
    }

    public function test_can_detect_valid_token()
    {
        $service = OAuthService::create([
            'name' => 'Test Google Drive',
            'service_type' => 'google_drive',
            'credentials' => ['client_id' => 'test', 'client_secret' => 'secret'],
            'access_token' => 'valid_token',
            'expires_at' => now()->addHour(),
            'is_active' => true,
        ]);

        $this->assertFalse($service->isTokenExpired());
    }

    public function test_returns_null_for_non_existent_service()
    {
        $oauthManager = new OAuthManager();
        $token = $oauthManager->getValidToken('non_existent_service');

        $this->assertNull($token);
    }

    public function test_returns_null_for_service_without_token()
    {
        OAuthService::create([
            'name' => 'Test Google Drive',
            'service_type' => 'google_drive',
            'credentials' => ['client_id' => 'test', 'client_secret' => 'secret'],
            'is_active' => true,
        ]);

        $oauthManager = new OAuthManager();
        $token = $oauthManager->getValidToken('google_drive');

        $this->assertNull($token);
    }

    protected function tearDown(): void
    {
        if (Schema::hasTable('oauth_services')) {
            Schema::drop('oauth_services');
        }

        parent::tearDown();
    }
}
