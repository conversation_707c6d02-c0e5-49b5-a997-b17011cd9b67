## Instalación

```bash
composer require lbcdev/oauth-manager
php artisan vendor:publish --tag=oauth-manager-config
php artisan migrate
```

---

## Configuración de Google OAuth

### 1. Crear un proyecto en Google Cloud Console

1. Ve a [Google Cloud Console](https://console.cloud.google.com/)
2. Haz clic en el selector de proyectos > **Nuevo Proyecto**
3. Ingresa un nombre y haz clic en **Crear**

### 2. Habilitar APIs necesarias

1. En el menú izquierdo, ve a **APIs y Servicios** > **Biblioteca**
2. Busca y habilita estas APIs:
    - **Google Drive API**
    - **Google OAuth 2.0 API**

### 3. Configurar pantalla de consentimiento OAuth

1. Ve a **APIs y Servicios** > **Pantalla de consentimiento OAuth**
2. Selecciona **Externo** > **Crear**
3. Completa la información requerida:
    - **Nombre de la aplicación**: El que mostrará Google a los usuarios
    - **Correo electrónico de soporte**: Tu email de contacto
    - **Dominios autorizados**: Añade tus dominios (ej: `tudominio.com`)
4. En **Ámbitos**, añade los necesarios:
    - `https://www.googleapis.com/auth/drive.file`
    - `https://www.googleapis.com/auth/drive.metadata.readonly`
5. En **Usuarios de prueba**, añade los emails que podrán acceder durante pruebas

### 4. Crear credenciales OAuth

1. Ve a **APIs y Servicios** > **Credenciales**
2. Haz clic en **+ Crear credenciales** > **ID de cliente OAuth**
3. Configura:
    - **Tipo de aplicación**: Aplicación web
    - **Nombre**: Un nombre descriptivo
    - **URI de redireccionamiento autorizados**:
        - Para desarrollo local: `http://localhost/oauth/google/callback`
        - Para producción: `https://tudominio.com/oauth/google/callback`
4. Haz clic en **Crear**

### 5. Configurar en tu aplicación Laravel

1. En tu archivo `config/oauth-manager.php`:

```php
'google_drive' => [
    'client_id' => env('GOOGLE_DRIVE_CLIENT_ID'),
    'client_secret' => env('GOOGLE_DRIVE_CLIENT_SECRET'),
    'redirect' => env('GOOGLE_DRIVE_REDIRECT_URI'),
    'scopes' => [
        'https://www.googleapis.com/auth/drive.file',
        'https://www.googleapis.com/auth/drive.metadata.readonly'
    ],
    'approval_prompt' => 'force',
    'access_type' => 'offline',
],
```

2. Añade a tu `.env`:

```
GOOGLE_DRIVE_CLIENT_ID=tu-client-id
GOOGLE_DRIVE_CLIENT_SECRET=tu-client-secret
GOOGLE_DRIVE_REDIRECT_URI=http://localhost/oauth/google/callback
```

### 6. Probar la conexión

1. Ejecuta el comando para iniciar el flujo OAuth:

```bash
php artisan oauth:connect google_drive
```

2. Sigue las instrucciones para autorizar la aplicación

### Solución de problemas

-   **Error "redirect_uri_mismatch"**: Verifica que las URIs en Google Cloud coincidan exactamente con las de tu `.env`
-   **Token no se refresca**: Asegúrate de tener `access_type=offline` en la configuración
-   **Permisos insuficientes**: Revisa los scopes solicitados y los asignados al cliente OAuth

---

## Configuración recomendada para producción

-   Restringe las credenciales OAuth por dominio/IP si es posible
-   Usa cuentas de servicio para operaciones server-to-server
-   Implementa rotación automática de secretos (disponible en Google Cloud)

🔗 [Documentación oficial de Google OAuth](https://developers.google.com/identity/protocols/oauth2)

---

## Uso básico

```php
// Obtener un token válido
$token = OAuthManager::getValidToken('google_drive', 'Mi Google Drive');

// Usar el token en tu aplicación
$client = new Google_Client();
$client->setAccessToken($token);
$driveService = new Google_Service_Drive($client);

// Subir archivo
$file = new Google_Service_Drive_DriveFile();
$file->setName('documento.pdf');
$result = $driveService->files->create($file, [
    'data' => file_get_contents('documento.pdf'),
    'mimeType' => 'application/pdf',
]);
```

## Añadir nuevos servicios

1. Crear provider que implemente OAuthProviderInterface
2. Añadir configuración en config/oauth-manager.php
3. Implementar los métodos necesarios

## Características

-   ✅ Gestión automática de tokens
-   ✅ Renovación automática de tokens
-   ✅ Interfaz Filament completa
-   ✅ Encriptación de credenciales
-   ✅ Múltiples servicios del mismo tipo
-   ✅ Middleware personalizable
-   ✅ Fácil extensión
