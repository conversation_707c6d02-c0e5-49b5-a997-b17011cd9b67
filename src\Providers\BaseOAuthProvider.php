<?php

namespace LBC<PERSON>ev\OAuthManager\Providers;

use LBCDev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Contracts\OAuthProviderInterface;

abstract class BaseOAuthProvider implements OAuthProviderInterface
{
    protected OAuthService $service;
    protected array $config;

    public function __construct(OAuthService $service)
    {
        $this->service = $service;
        $this->config = config("oauth-manager.services.{$service->service_type}");
    }

    protected function getRedirectUri(): string
    {
        return route('oauth-manager.callback', ['service' => $this->service]);
    }

    abstract public function getAuthorizationUrl(): string;
    abstract public function handleCallback(string $code): array;
    abstract public function refreshToken(): ?array;
    abstract public function revokeToken(): bool;
    abstract public function testConnection(): bool;
}
