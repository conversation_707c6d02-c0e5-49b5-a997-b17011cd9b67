{"name": "lbcdev/oauth-manager", "description": "Gestión de cuentas y tokens OAuth de servicios externos", "type": "library", "license": "MIT", "authors": [{"name": "Luis <PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2", "laravel/framework": "^12.0", "league/oauth2-client": "^2.7", "league/oauth2-google": "^4.0", "google/apiclient": "^2.15"}, "require-dev": {"phpunit/phpunit": "^11.0"}, "scripts": {"pre-autoload-dump": "Google\\Task\\Composer::cleanup"}, "autoload": {"psr-4": {"LBCDev\\OAuthManager\\": "src/"}}, "autoload-dev": {"psr-4": {"LBCDev\\OAuthManager\\Tests\\": "tests/"}}, "extra": {"laravel": {"providers": ["LBCDev\\OAuthManager\\OAuthManagerServiceProvider"]}, "google/apiclient-services": ["Drive", "YouTube"]}, "minimum-stability": "stable", "prefer-stable": true}